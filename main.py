from fastapi import FastAPI, HTTPException
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
import time
import os
import platform
import undetected_chromedriver as uc
from selenium_stealth import stealth
import threading
import logging
from typing import Optional
from datetime import datetime
import glob
import schedule

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


app = FastAPI(title="reCAPTCHA Token API", version="1.0.0")

class RecaptchaManager:
    def __init__(self):
        self.driver: Optional[webdriver.Chrome] = None
        self.lock = threading.Lock()
        self.is_initializing = False  # 标记是否正在初始化
        self.initialization_lock = threading.Lock()  # 初始化专用锁

        # 根据操作系统设置驱动路径
        system = platform.system().lower()
        if system == "linux":
            self.driver_path = os.path.join(os.getcwd(), "driver", "chromedriver")
        else:  # Windows
            self.driver_path = os.path.join(os.getcwd(), "driver", "chromedriver.exe")

        self.screenshots_dir = os.path.join(os.getcwd(), "screenshots")
        self.ensure_screenshots_dir()
        self.start_cleanup_scheduler()

    def ensure_screenshots_dir(self):
        """确保截图目录存在"""
        if not os.path.exists(self.screenshots_dir):
            os.makedirs(self.screenshots_dir)
            logger.info(f"创建截图目录: {self.screenshots_dir}")

    def start_cleanup_scheduler(self):
        """启动定期清理任务"""
        schedule.every(24).hours.do(self.cleanup_screenshots)

        # 启动后台线程运行调度器
        def run_scheduler():
            while True:
                schedule.run_pending()
                time.sleep(3600)  # 每小时检查一次

        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        logger.info("截图清理调度器已启动，每24小时清理一次")

    def cleanup_screenshots(self):
        """清理截图目录"""
        try:
            screenshot_files = glob.glob(os.path.join(self.screenshots_dir, "*.png"))
            deleted_count = 0

            for file_path in screenshot_files:
                try:
                    os.remove(file_path)
                    deleted_count += 1
                except Exception as e:
                    logger.warning(f"删除截图文件失败 {file_path}: {e}")

            logger.info(f"截图清理完成，删除了 {deleted_count} 个文件")
        except Exception as e:
            logger.error(f"截图清理失败: {e}")

    def take_screenshot(self, suffix: str = "") -> str:
        """截图并保存到screenshots目录"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}{suffix}.png"
            screenshot_path = os.path.join(self.screenshots_dir, filename)

            self.driver.save_screenshot(screenshot_path)
            logger.info(f"截图已保存: {screenshot_path}")
            return screenshot_path
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return ""
        
    def create_driver(self):
        """创建新的Chrome驱动实例"""
        chrome_options = webdriver.ChromeOptions()

        # 根据操作系统设置Chrome二进制文件路径
        system = platform.system().lower()
        if system == "linux":
            chrome_options.binary_location = os.path.join(os.getcwd(), "chrome", "chrome-linux64", "chrome")
        else:  # Windows
            chrome_options.binary_location = os.path.join(os.getcwd(), "chrome", "chrome-win64", "chrome.exe")

        # 不使用无头模式，保持网页打开状态
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')

        # 检查是否设置了代理环境变量
        proxy = os.getenv('proxy')
        if proxy:
            logger.info(f"使用代理: {proxy}")
            chrome_options.add_argument(f'--proxy-server={proxy}')

        prefs = {"profile.managed_default_content_settings.images": 2}
        chrome_options.add_experimental_option("prefs", prefs)

        driver = uc.Chrome(driver_executable_path=self.driver_path, options=chrome_options)
        
        stealth(driver,
                languages=["en-US", "en"],
                vendor="Google Inc.",
                platform="Win32",
                webgl_vendor="Google Inc. (NVIDIA)",
                renderer="ANGLE (NVIDIA, NVIDIA GeForce RTX 4060 (0x00002882) Direct3D11 vs_5_0 ps_5_0, D3D11)",
                fix_hairline=True,
                )

        driver.execute_cdp_cmd('Network.setBlockedURLs', {
            'urls': ['.css', '*.css', 'https://*.css', 'http://*.css','.woff2', '*.woff2', 'https://*.woff2', 'http://*.woff2']
        })
        driver.execute_cdp_cmd('Network.enable', {})

        return driver
    
    def open_website(self):
        """打开网站并等待加载"""
        try:
            logger.info("正在打开 https://www.genspark.ai ...")
            self.driver.get("https://www.genspark.ai")

            # 等待页面加载
            time.sleep(20)
            logger.info("网站已打开")

            # 截图保存
            screenshot_path = self.take_screenshot("_website_opened")
            if screenshot_path:
                logger.info(f"网站打开后截图已保存: {screenshot_path}")

            # 检查grecaptcha是否存在
            grecaptcha_exists = self.driver.execute_script("return typeof grecaptcha !== 'undefined';")
            if not grecaptcha_exists:
                logger.warning("grecaptcha未定义，可能页面还未完全加载reCAPTCHA")
                # 再等待一段时间
                time.sleep(10)
                grecaptcha_exists = self.driver.execute_script("return typeof grecaptcha !== 'undefined';")

                # 如果还是没有加载，再截图一次
                if not grecaptcha_exists:
                    screenshot_path = self.take_screenshot("_recaptcha_not_loaded")
                    if screenshot_path:
                        logger.info(f"reCAPTCHA未加载时截图已保存: {screenshot_path}")

            return grecaptcha_exists
        except Exception as e:
            logger.error(f"打开网站时发生错误: {e}")
            return False
    
    def get_recaptcha_token(self):
        """获取reCAPTCHA token"""
        try:
            # 执行reCAPTCHA脚本
            self.driver.execute_script("""
                window.recaptchaToken = null;
                grecaptcha.ready(function() {
                    grecaptcha.execute('6Leq7KYqAAAAAGdd1NaUBJF9dHTPAKP7DcnaRc66', {action: 'submit'}).then(function(token) {
                        console.log('reCAPTCHA token:', token);
                        window.recaptchaToken = token;
                    });
                });
            """)
            
            # 等待token生成（最多等待15秒）
            for i in range(10):
                time.sleep(1)
                token = self.driver.execute_script("return window.recaptchaToken;")
                if token:
                    logger.info(f"成功获取到reCAPTCHA token: {token[:50]}...")
                    return token
            
            logger.warning("等待token超时")
            return None
            
        except Exception as e:
            logger.error(f"获取token时发生错误: {e}")
            return None
    
    def ensure_driver_ready(self):
        """确保驱动已准备好，支持并发请求"""
        # 快速检查：如果驱动已存在且正常，直接返回
        if self.driver is not None:
            try:
                self.driver.current_url
                return  # 驱动正常，直接返回
            except:
                logger.info("驱动已失效，需要重新创建")

        # 使用初始化锁防止多个线程同时初始化
        with self.initialization_lock:
            # 双重检查：可能其他线程已经完成了初始化
            if self.driver is not None:
                try:
                    self.driver.current_url
                    return  # 其他线程已经完成初始化
                except:
                    pass  # 驱动仍然无效，继续初始化

            # 检查是否已经有线程在初始化
            if self.is_initializing:
                logger.info("其他线程正在初始化驱动，当前线程等待...")
                # 等待初始化完成，最多等待60秒
                for i in range(60):
                    time.sleep(1)
                    if not self.is_initializing and self.driver is not None:
                        try:
                            self.driver.current_url
                            logger.info("等待初始化完成，驱动已就绪")
                            return
                        except:
                            break
                raise Exception("等待驱动初始化超时或失败")

            # 开始初始化
            self.is_initializing = True
            try:
                logger.info("开始创建新的Chrome驱动实例")

                # 如果有旧驱动，先关闭
                if self.driver:
                    try:
                        self.driver.quit()
                    except:
                        pass
                    self.driver = None

                # 创建新驱动
                self.driver = self.create_driver()
                if not self.open_website():
                    raise Exception("无法打开网站或加载reCAPTCHA")

                logger.info("驱动初始化完成")
            finally:
                self.is_initializing = False
    
    def close_driver(self):
        """关闭驱动"""
        with self.initialization_lock:
            self.is_initializing = False  # 确保重置初始化状态
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None
                logger.info("驱动已关闭")

# 创建全局管理器实例
recaptcha_manager = RecaptchaManager()

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "reCAPTCHA Token API",
        "version": "1.1.0",
        "features": [
            "自动截图保存",
            "24小时定期清理截图",
            "手动截图和清理接口"
        ],
        "endpoints": {
            "/get-token": "获取reCAPTCHA token",
            "/health": "健康检查（包含截图统计）",
            "/screenshot": "手动截图 (POST)",
            "/cleanup-screenshots": "手动清理截图 (POST)"
        }
    }

@app.get("/get-token")
async def get_recaptcha_token():
    """获取reCAPTCHA token的API接口"""
    try:
        # 确保驱动已准备好
        recaptcha_manager.ensure_driver_ready()
        
        # 获取token
        token = recaptcha_manager.get_recaptcha_token()
        
        if token:
            return {
                "success": True,
                "token": token,
                "message": "成功获取reCAPTCHA token"
            }
        else:
            # 如果获取失败，尝试重新打开网页
            logger.info("获取token失败，尝试重新打开网页")
            recaptcha_manager.close_driver()
            recaptcha_manager.ensure_driver_ready()
            
            # 再次尝试获取token
            token = recaptcha_manager.get_recaptcha_token()
            
            if token:
                return {
                    "success": True,
                    "token": token,
                    "message": "重新打开网页后成功获取reCAPTCHA token"
                }
            else:
                raise HTTPException(status_code=500, detail="无法获取reCAPTCHA token")
                
    except Exception as e:
        logger.error(f"API调用失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取token失败: {str(e)}")

@app.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        if recaptcha_manager.driver:
            # 检查驱动是否正常
            url = recaptcha_manager.driver.current_url
            # 获取截图目录信息
            screenshot_count = len(glob.glob(os.path.join(recaptcha_manager.screenshots_dir, "*.png")))
            return {
                "status": "healthy",
                "driver_active": True,
                "current_url": url,
                "screenshots_dir": recaptcha_manager.screenshots_dir,
                "screenshot_count": screenshot_count
            }
        else:
            screenshot_count = len(glob.glob(os.path.join(recaptcha_manager.screenshots_dir, "*.png")))
            return {
                "status": "healthy",
                "driver_active": False,
                "message": "驱动未初始化",
                "screenshots_dir": recaptcha_manager.screenshots_dir,
                "screenshot_count": screenshot_count
            }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }

@app.post("/screenshot")
async def take_screenshot():
    """手动截图接口"""
    try:
        if not recaptcha_manager.driver:
            raise HTTPException(status_code=400, detail="浏览器未初始化，请先调用get-token接口")

        screenshot_path = recaptcha_manager.take_screenshot("_manual")
        if screenshot_path:
            return {
                "success": True,
                "screenshot_path": screenshot_path,
                "message": "截图成功"
            }
        else:
            raise HTTPException(status_code=500, detail="截图失败")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"截图API调用失败: {e}")
        raise HTTPException(status_code=500, detail=f"截图失败: {str(e)}")

@app.post("/cleanup-screenshots")
async def cleanup_screenshots():
    """手动清理截图接口"""
    try:
        screenshot_files = glob.glob(os.path.join(recaptcha_manager.screenshots_dir, "*.png"))
        before_count = len(screenshot_files)

        recaptcha_manager.cleanup_screenshots()

        # 重新统计
        screenshot_files_after = glob.glob(os.path.join(recaptcha_manager.screenshots_dir, "*.png"))
        after_count = len(screenshot_files_after)
        deleted_count = before_count - after_count

        return {
            "success": True,
            "deleted_count": deleted_count,
            "remaining_count": after_count,
            "message": f"清理完成，删除了 {deleted_count} 个截图文件"
        }
    except Exception as e:
        logger.error(f"清理截图API调用失败: {e}")
        raise HTTPException(status_code=500, detail=f"清理截图失败: {str(e)}")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理资源"""
    logger.info("正在关闭应用...")
    recaptcha_manager.close_driver()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8042)
