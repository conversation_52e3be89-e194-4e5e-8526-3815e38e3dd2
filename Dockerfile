# 使用Python 3.11官方镜像作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive
ENV DISPLAY=:99

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    # Chrome浏览器依赖
    wget \
    gnupg \
    unzip \
    curl \
    xvfb \
    x11vnc \
    fluxbox \
    # 字体支持
    fonts-liberation \
    fonts-noto-cjk \
    # 其他必要工具
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 安装Google Chrome
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# 下载并安装ChromeDriver
RUN CHROME_VERSION=$(google-chrome --version | cut -d " " -f3 | cut -d "." -f1-3) \
    && CHROMEDRIVER_VERSION=$(curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_${CHROME_VERSION}") \
    && wget -O /tmp/chromedriver.zip "https://chromedriver.storage.googleapis.com/${CHROMEDRIVER_VERSION}/chromedriver_linux64.zip" \
    && unzip /tmp/chromedriver.zip -d /tmp/ \
    && mv /tmp/chromedriver /usr/local/bin/chromedriver \
    && chmod +x /usr/local/bin/chromedriver \
    && rm /tmp/chromedriver.zip

# 复制requirements.txt并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /app/screenshots \
    && mkdir -p /app/temp_chrome_profile \
    && mkdir -p /app/driver

# 如果存在本地chromedriver，复制到容器中
# 注意：在Linux容器中需要使用Linux版本的chromedriver
RUN if [ -f "/app/driver/chromedriver.exe" ]; then \
        echo "Windows chromedriver detected, will use system chromedriver instead"; \
    fi

# 设置chromedriver路径为系统路径
RUN ln -sf /usr/local/bin/chromedriver /app/driver/chromedriver

# 创建启动脚本
RUN echo '#!/bin/bash\n\
# 启动Xvfb虚拟显示\n\
Xvfb :99 -screen 0 1920x1080x24 &\n\
\n\
# 等待Xvfb启动\n\
sleep 2\n\
\n\
# 启动应用\n\
exec python start_api.py' > /app/start.sh \
    && chmod +x /app/start.sh

# 暴露端口
EXPOSE 8042

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8042/health || exit 1

# 启动应用
CMD ["/app/start.sh"]
