version: '3.8'

services:
  recaptcha-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: recaptcha-api
    ports:
      - "8042:8042"
    environment:
      # 可选的代理设置，如果需要使用代理请取消注释并修改
      # - proxy=http://127.0.0.1:7890
      - PYTHONUNBUFFERED=1
      - DISPLAY=:99
    volumes:
      - ./screenshots:/app/screenshots
      - ./temp_chrome_profile:/app/temp_chrome_profile
      - ./driver:/app/driver
      - ./fingerprint-chromium:/app/fingerprint-chromium
    depends_on:
      - xvfb
    networks:
      - recaptcha-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8042/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # X虚拟帧缓冲服务，为Chrome提供显示环境
  xvfb:
    image: selenium/standalone-chrome:latest
    container_name: xvfb-display
    environment:
      - DISPLAY=:99
      - SCREEN_WIDTH=1920
      - SCREEN_HEIGHT=1080
      - SCREEN_DEPTH=24
    ports:
      - "4444:4444"  # Selenium Grid端口（可选）
      - "7900:7900"  # VNC端口，用于远程查看浏览器（可选）
    volumes:
      - /dev/shm:/dev/shm
    networks:
      - recaptcha-network
    restart: unless-stopped
    command: >
      bash -c "
        Xvfb :99 -screen 0 1920x1080x24 &
        x11vnc -display :99 -nopw -listen localhost -xkb -rfbport 5900 -rfbportv6 5900 -shared &
        /opt/bin/entry_point.sh
      "

networks:
  recaptcha-network:
    driver: bridge

volumes:
  screenshots:
    driver: local
  chrome_profile:
    driver: local
